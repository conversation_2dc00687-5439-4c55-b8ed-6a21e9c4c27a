import { useCallback, useRef } from 'react';

interface BarcodeDetectionOptions {
  minLength?: number; // Comprimento mínimo para considerar como código
  maxTimeBetweenChars?: number; // Tempo máximo entre caracteres (ms) para considerar como leitor
  minSpeed?: number; // Velocidade mínima de caracteres por segundo
}

interface BarcodeDetectionResult {
  isFromScanner: boolean;
  inputValue: string;
}

export const useBarcodeDetection = (options: BarcodeDetectionOptions = {}) => {
  const {
    minLength = 8,
    maxTimeBetweenChars = 50, // 50ms entre caracteres é típico de leitores
    minSpeed = 10, // 10 caracteres por segundo mínimo
  } = options;

  const inputStartTime = useRef<number>(0);
  const lastCharTime = useRef<number>(0);
  const charTimes = useRef<number[]>([]);
  const inputBuffer = useRef<string>('');
  const isTypingFast = useRef<boolean>(false);

  const detectBarcodeInput = useCallback(
    (inputValue: string): BarcodeDetectionResult => {
      const now = Date.now();
      const currentLength = inputValue.length;
      const previousLength = inputBuffer.current.length;

      // Se é o primeiro caractere ou o input foi limpo
      if (currentLength === 1 || currentLength < previousLength) {
        inputStartTime.current = now;
        lastCharTime.current = now;
        charTimes.current = [now];
        inputBuffer.current = inputValue;
        isTypingFast.current = false;
        return { isFromScanner: false, inputValue };
      }

      // Se adicionou caracteres
      if (currentLength > previousLength) {
        const timeSinceLastChar = now - lastCharTime.current;
        charTimes.current.push(now);
        lastCharTime.current = now;
        inputBuffer.current = inputValue;

        // Verifica se o tempo entre caracteres é muito rápido (típico de leitor)
        if (timeSinceLastChar > 0 && timeSinceLastChar <= maxTimeBetweenChars) {
          isTypingFast.current = true;
        } else if (timeSinceLastChar > maxTimeBetweenChars * 3) {
          // Se demorou muito entre caracteres, provavelmente é digitação manual
          isTypingFast.current = false;
        }
      }

      // Só avalia se tem comprimento mínimo
      if (currentLength < minLength) {
        return { isFromScanner: false, inputValue };
      }

      // Calcula a velocidade média de digitação
      const totalTime = now - inputStartTime.current;
      const avgSpeed = totalTime > 0 ? (currentLength / totalTime) * 1000 : 0; // chars por segundo

      // Verifica se foi digitação rápida e consistente (típico de leitor)
      const isFromScanner =
        isTypingFast.current &&
        avgSpeed >= minSpeed &&
        charTimes.current.length >= minLength;

      return {
        isFromScanner,
        inputValue,
      };
    },
    [maxTimeBetweenChars, minLength, minSpeed]
  );

  const resetDetection = useCallback(() => {
    inputStartTime.current = 0;
    lastCharTime.current = 0;
    charTimes.current = [];
    inputBuffer.current = '';
    isTypingFast.current = false;
  }, []);

  return {
    detectBarcodeInput,
    resetDetection,
  };
};
